using System;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Volo.Abp.Application.Services;

namespace FbAutoReplyPlatformExpress.Services;

public interface IFacebookAuthService : IApplicationService
{
    Task<FacebookUserDto> GetCurrentUserFacebookInfoAsync();
    Task<FacebookUserDto> CreateOrUpdateFacebookUserAsync(CreateFacebookUserDto input);
    Task<string> GetFacebookLoginUrlAsync(string redirectUri);
    Task<FacebookUserDto> HandleFacebookCallbackAsync(string code, string redirectUri);
    Task RefreshFacebookTokenAsync(Guid facebookUserId);
    Task DisconnectFacebookAsync();
    Task<bool> IsConnectedToFacebookAsync();
}
