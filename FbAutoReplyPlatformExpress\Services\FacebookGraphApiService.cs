using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Volo.Abp.DependencyInjection;

namespace FbAutoReplyPlatformExpress.Services;

public class FacebookGraphApiService : ITransientDependency
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<FacebookGraphApiService> _logger;
    private const string GraphApiBaseUrl = "https://graph.facebook.com/v18.0";

    public FacebookGraphApiService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<FacebookGraphApiService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<FacebookUserInfo> GetUserInfoAsync(string accessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me?fields=id,name,email,picture&access_token={accessToken}";
            var response = await _httpClient.GetStringAsync(url);
            return JsonConvert.DeserializeObject<FacebookUserInfo>(response)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook user info");
            throw;
        }
    }

    public async Task<List<FacebookPageInfo>> GetUserPagesAsync(string accessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me/accounts?fields=id,name,access_token,picture,category,fan_count&access_token={accessToken}";
            var response = await _httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<FacebookPagesResponse>(response)!;
            return result.Data ?? new List<FacebookPageInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook user pages");
            throw;
        }
    }

    public async Task<List<FacebookPostInfo>> GetPagePostsAsync(string pageId, string pageAccessToken, int limit = 25)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/posts?fields=id,message,created_time,attachments,likes.summary(true),comments.summary(true),shares,permalink_url,picture,full_picture&limit={limit}&access_token={pageAccessToken}";
            var response = await _httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<FacebookPostsResponse>(response)!;
            return result.Data ?? new List<FacebookPostInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook page posts");
            throw;
        }
    }

    public async Task<string> PostCommentReplyAsync(string commentId, string message, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{commentId}/comments";
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("message", message),
                new KeyValuePair<string, string>("access_token", pageAccessToken)
            });

            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to post comment reply: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new Exception($"Failed to post comment reply: {response.StatusCode}");
            }

            var result = JsonConvert.DeserializeObject<FacebookCommentResponse>(responseContent)!;
            return result.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error posting comment reply");
            throw;
        }
    }

    public async Task<string> SendPrivateMessageAsync(string recipientId, string message, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me/messages";
            var payload = new
            {
                recipient = new { id = recipientId },
                message = new { text = message }
            };

            var content = new StringContent(JsonConvert.SerializeObject(payload), Encoding.UTF8, "application/json");
            content.Headers.Add("Authorization", $"Bearer {pageAccessToken}");

            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to send private message: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new Exception($"Failed to send private message: {response.StatusCode}");
            }

            var result = JsonConvert.DeserializeObject<FacebookMessageResponse>(responseContent)!;
            return result.MessageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending private message");
            throw;
        }
    }

    public async Task<bool> SubscribeToWebhookAsync(string pageId, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/subscribed_apps";
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("subscribed_fields", "feed,mention"),
                new KeyValuePair<string, string>("access_token", pageAccessToken)
            });

            var response = await _httpClient.PostAsync(url, content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to webhook");
            throw;
        }
    }

    public async Task<bool> UnsubscribeFromWebhookAsync(string pageId, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/subscribed_apps?access_token={pageAccessToken}";
            var response = await _httpClient.DeleteAsync(url);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from webhook");
            throw;
        }
    }

    public string GetFacebookLoginUrl(string redirectUri)
    {
        var appId = _configuration["Facebook:AppId"];
        var scope = "pages_manage_posts,pages_read_engagement,pages_manage_metadata,pages_messaging";
        
        return $"https://www.facebook.com/v18.0/dialog/oauth?" +
               $"client_id={appId}&" +
               $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
               $"scope={Uri.EscapeDataString(scope)}&" +
               $"response_type=code";
    }

    public async Task<FacebookTokenResponse> ExchangeCodeForTokenAsync(string code, string redirectUri)
    {
        try
        {
            var appId = _configuration["Facebook:AppId"];
            var appSecret = _configuration["Facebook:AppSecret"];
            
            var url = $"{GraphApiBaseUrl}/oauth/access_token?" +
                     $"client_id={appId}&" +
                     $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                     $"client_secret={appSecret}&" +
                     $"code={code}";

            var response = await _httpClient.GetStringAsync(url);
            return JsonConvert.DeserializeObject<FacebookTokenResponse>(response)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exchanging code for token");
            throw;
        }
    }
}
