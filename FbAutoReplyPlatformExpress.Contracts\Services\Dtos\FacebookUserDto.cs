using System;
using Volo.Abp.Application.Dtos;

namespace FbAutoReplyPlatformExpress.Services.Dtos;

public class FacebookUserDto : FullAuditedEntityDto<Guid>
{
    public string FacebookId { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string FacebookEmail { get; set; } = string.Empty;
    public string FacebookName { get; set; } = string.Empty;
    public string ProfilePictureUrl { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
}

public class CreateFacebookUserDto
{
    public string FacebookId { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string AccessToken { get; set; } = string.Empty;
    public string FacebookEmail { get; set; } = string.Empty;
    public string FacebookName { get; set; } = string.Empty;
    public string? RefreshToken { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
    public string? ProfilePictureUrl { get; set; }
}

public class UpdateFacebookUserDto
{
    public string? FacebookName { get; set; }
    public string? ProfilePictureUrl { get; set; }
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
}
