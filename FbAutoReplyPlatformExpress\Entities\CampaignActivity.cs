using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace FbAutoReplyPlatformExpress.Entities;

public class CampaignActivity : FullAuditedAggregateRoot<Guid>
{
    [Required]
    public Guid CampaignId { get; set; }

    [Required]
    [StringLength(256)]
    public string FacebookCommentId { get; set; } = string.Empty;

    [Required]
    [StringLength(256)]
    public string CommenterFacebookId { get; set; } = string.Empty;

    [StringLength(256)]
    public string CommenterName { get; set; } = string.Empty;

    [StringLength(2048)]
    public string OriginalComment { get; set; } = string.Empty;

    [StringLength(2048)]
    public string? PublicReplyMessage { get; set; }

    [StringLength(2048)]
    public string? PrivateReplyMessage { get; set; }

    [StringLength(256)]
    public string? PublicReplyId { get; set; }

    [StringLength(256)]
    public string? PrivateMessageId { get; set; }

    public bool PublicReplySent { get; set; } = false;

    public bool PrivateReplySent { get; set; } = false;

    public DateTime? PublicReplyAt { get; set; }

    public DateTime? PrivateReplyAt { get; set; }

    [StringLength(512)]
    public string? ErrorMessage { get; set; }

    public bool HasError { get; set; } = false;

    public int RetryCount { get; set; } = 0;

    public DateTime CommentCreatedAt { get; set; }

    // Navigation property
    public virtual AutoReplyCampaign Campaign { get; set; } = null!;

    protected CampaignActivity()
    {
        // For EF Core
    }

    public CampaignActivity(
        Guid id,
        Guid campaignId,
        string facebookCommentId,
        string commenterFacebookId,
        string commenterName,
        string originalComment,
        DateTime commentCreatedAt) : base(id)
    {
        CampaignId = campaignId;
        FacebookCommentId = facebookCommentId;
        CommenterFacebookId = commenterFacebookId;
        CommenterName = commenterName;
        OriginalComment = originalComment;
        CommentCreatedAt = commentCreatedAt;
        PublicReplySent = false;
        PrivateReplySent = false;
        HasError = false;
        RetryCount = 0;
    }

    public void SetPublicReply(string replyMessage, string replyId)
    {
        PublicReplyMessage = replyMessage;
        PublicReplyId = replyId;
        PublicReplySent = true;
        PublicReplyAt = DateTime.UtcNow;
        ClearError();
    }

    public void SetPrivateReply(string replyMessage, string messageId)
    {
        PrivateReplyMessage = replyMessage;
        PrivateMessageId = messageId;
        PrivateReplySent = true;
        PrivateReplyAt = DateTime.UtcNow;
        ClearError();
    }

    public void SetError(string errorMessage)
    {
        ErrorMessage = errorMessage;
        HasError = true;
        RetryCount++;
    }

    public void ClearError()
    {
        ErrorMessage = null;
        HasError = false;
    }

    public bool CanRetry(int maxRetries = 3)
    {
        return HasError && RetryCount < maxRetries;
    }
}
