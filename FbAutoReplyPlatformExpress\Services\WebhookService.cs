using System;
using System.Linq;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Volo.Abp.Application.Services;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;

namespace FbAutoReplyPlatformExpress.Services;

public class WebhookService : ApplicationService, IWebhookService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<WebhookService> _logger;
    private readonly IBackgroundJobManager _backgroundJobManager;
    private readonly IRepository<AutoReplyCampaign, Guid> _campaignRepository;
    private readonly IRepository<FacebookPost, Guid> _postRepository;

    public WebhookService(
        IConfiguration configuration,
        ILogger<WebhookService> logger,
        IBackgroundJobManager backgroundJobManager,
        IRepository<AutoReplyCampaign, Guid> campaignRepository,
        IRepository<FacebookPost, Guid> postRepository)
    {
        _configuration = configuration;
        _logger = logger;
        _backgroundJobManager = backgroundJobManager;
        _campaignRepository = campaignRepository;
        _postRepository = postRepository;
    }

    public async Task<string> VerifyWebhookAsync(string mode, string token, string challenge)
    {
        var verifyToken = _configuration["Facebook:WebhookVerifyToken"];
        
        if (mode == "subscribe" && token == verifyToken)
        {
            _logger.LogInformation("Facebook webhook verified successfully");
            return challenge;
        }
        
        _logger.LogWarning("Facebook webhook verification failed. Mode: {Mode}, Token: {Token}", mode, token);
        return string.Empty;
    }

    public async Task ProcessWebhookAsync(string payload, string signature)
    {
        try
        {
            var webhookPayload = JsonConvert.DeserializeObject<FacebookWebhookPayload>(payload);
            
            if (webhookPayload?.Object != "page" || webhookPayload.Entry == null)
            {
                _logger.LogWarning("Received non-page webhook or invalid payload");
                return;
            }

            foreach (var entry in webhookPayload.Entry)
            {
                if (entry.Changes == null) continue;

                foreach (var change in entry.Changes)
                {
                    if (change.Field == "feed" && change.Value != null)
                    {
                        await ProcessFeedChangeAsync(change.Value);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing webhook payload: {Payload}", payload);
            throw;
        }
    }

    public async Task ProcessCommentAsync(WebhookCommentDto comment)
    {
        try
        {
            _logger.LogInformation("Processing comment {CommentId} on post {PostId}", comment.CommentId, comment.PostId);

            // Find the post and check if it has an active campaign
            var post = await _postRepository.FirstOrDefaultAsync(p => p.FacebookPostId == comment.PostId);
            if (post == null)
            {
                _logger.LogWarning("Post {PostId} not found in database", comment.PostId);
                return;
            }

            var campaign = await _campaignRepository.FirstOrDefaultAsync(c => 
                c.FacebookPostId == post.Id && c.IsActive && c.IsValidForReply());
            
            if (campaign == null)
            {
                _logger.LogInformation("No active campaign found for post {PostId}", comment.PostId);
                return;
            }

            // Queue background job to process the auto-reply
            await _backgroundJobManager.EnqueueAsync(new ProcessAutoReplyJobArgs
            {
                CampaignId = campaign.Id,
                CommentId = comment.CommentId,
                CommenterFacebookId = comment.CommenterFacebookId,
                CommenterName = comment.CommenterName,
                OriginalComment = comment.Message,
                CommentCreatedAt = comment.CreatedTime
            });

            _logger.LogInformation("Queued auto-reply job for comment {CommentId}", comment.CommentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing comment {CommentId}", comment.CommentId);
            throw;
        }
    }

    private async Task ProcessFeedChangeAsync(FacebookWebhookValue value)
    {
        try
        {
            if (value.Item == "comment" && !string.IsNullOrEmpty(value.CommentId))
            {
                var comment = new WebhookCommentDto
                {
                    CommentId = value.CommentId,
                    PostId = value.PostId ?? string.Empty,
                    CommenterFacebookId = value.SenderId ?? string.Empty,
                    CommenterName = value.SenderName ?? string.Empty,
                    Message = value.Message ?? string.Empty,
                    CreatedTime = value.CreatedTime.HasValue 
                        ? DateTimeOffset.FromUnixTimeSeconds(value.CreatedTime.Value).DateTime 
                        : DateTime.UtcNow
                };

                await ProcessCommentAsync(comment);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing feed change");
            throw;
        }
    }
}

public class ProcessAutoReplyJobArgs
{
    public Guid CampaignId { get; set; }
    public string CommentId { get; set; } = string.Empty;
    public string CommenterFacebookId { get; set; } = string.Empty;
    public string CommenterName { get; set; } = string.Empty;
    public string OriginalComment { get; set; } = string.Empty;
    public DateTime CommentCreatedAt { get; set; }
}
