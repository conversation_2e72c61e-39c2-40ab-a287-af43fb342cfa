using System;
using Volo.Abp.Application.Dtos;

namespace FbAutoReplyPlatformExpress.Services.Dtos;

public class AutoReplyCampaignDto : FullAuditedEntityDto<Guid>
{
    public Guid FacebookPostId { get; set; }
    public string CampaignName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string PublicReplyMessage { get; set; } = string.Empty;
    public string? PrivateReplyMessage { get; set; }
    public bool IsActive { get; set; }
    public bool SendPublicReply { get; set; }
    public bool SendPrivateReply { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int MaxRepliesPerUser { get; set; }
    public int TotalRepliesSent { get; set; }
    public int PublicRepliesSent { get; set; }
    public int PrivateRepliesSent { get; set; }
    public DateTime? LastReplyAt { get; set; }
    
    // Additional properties for UI
    public string PostMessage { get; set; } = string.Empty;
    public string PageName { get; set; } = string.Empty;
}

public class CreateAutoReplyCampaignDto
{
    public Guid FacebookPostId { get; set; }
    public string CampaignName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string PublicReplyMessage { get; set; } = string.Empty;
    public string? PrivateReplyMessage { get; set; }
    public bool SendPublicReply { get; set; } = true;
    public bool SendPrivateReply { get; set; } = false;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int MaxRepliesPerUser { get; set; } = 1;
}

public class UpdateAutoReplyCampaignDto
{
    public string? CampaignName { get; set; }
    public string? Description { get; set; }
    public string? PublicReplyMessage { get; set; }
    public string? PrivateReplyMessage { get; set; }
    public bool? SendPublicReply { get; set; }
    public bool? SendPrivateReply { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? MaxRepliesPerUser { get; set; }
}

public class GetCampaignsInput : PagedAndSortedResultRequestDto
{
    public Guid? FacebookPostId { get; set; }
    public string? SearchText { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
