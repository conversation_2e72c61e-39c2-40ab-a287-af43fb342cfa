﻿using Microsoft.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TextTemplateManagement.EntityFrameworkCore;
using Volo.Abp.LanguageManagement.EntityFrameworkCore;
using FbAutoReplyPlatformExpress.Entities;

namespace FbAutoReplyPlatformExpress.Data;

public class FbAutoReplyPlatformExpressDbContext : AbpDbContext<FbAutoReplyPlatformExpressDbContext>
{

    public const string DbTablePrefix = "App";
    public const string DbSchema = null;

    // Facebook-related DbSets
    public DbSet<FacebookUser> FacebookUsers { get; set; }
    public DbSet<FacebookPage> FacebookPages { get; set; }
    public DbSet<FacebookPost> FacebookPosts { get; set; }
    public DbSet<AutoReplyCampaign> AutoReplyCampaigns { get; set; }
    public DbSet<CampaignActivity> CampaignActivities { get; set; }

    public FbAutoReplyPlatformExpressDbContext(DbContextOptions<FbAutoReplyPlatformExpressDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigurePermissionManagement();
        builder.ConfigureBlobStoring();
        builder.ConfigureIdentityPro();
        builder.ConfigureOpenIddictPro();
        builder.ConfigureLanguageManagement();
        builder.ConfigureTextTemplateManagement();

        /* Configure your own entities here */
        ConfigureFacebookEntities(builder);
    }

    private void ConfigureFacebookEntities(ModelBuilder builder)
    {
        builder.Entity<FacebookUser>(b =>
        {
            b.ToTable(DbTablePrefix + "FacebookUsers", DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.FacebookId).IsRequired().HasMaxLength(256);
            b.Property(x => x.AccessToken).HasMaxLength(512);
            b.Property(x => x.RefreshToken).HasMaxLength(512);
            b.Property(x => x.FacebookEmail).HasMaxLength(256);
            b.Property(x => x.FacebookName).HasMaxLength(256);
            b.Property(x => x.ProfilePictureUrl).HasMaxLength(512);

            b.HasIndex(x => x.FacebookId).IsUnique();
            b.HasIndex(x => x.UserId);
        });

        builder.Entity<FacebookPage>(b =>
        {
            b.ToTable(DbTablePrefix + "FacebookPages", DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.FacebookPageId).IsRequired().HasMaxLength(256);
            b.Property(x => x.PageName).IsRequired().HasMaxLength(256);
            b.Property(x => x.PageAccessToken).HasMaxLength(512);
            b.Property(x => x.PageProfilePictureUrl).HasMaxLength(512);
            b.Property(x => x.Category).HasMaxLength(256);
            b.Property(x => x.WebhookSubscriptionId).HasMaxLength(1024);

            b.HasIndex(x => x.FacebookPageId).IsUnique();
            b.HasIndex(x => x.FacebookUserId);

            b.HasOne(x => x.FacebookUser)
                .WithMany()
                .HasForeignKey(x => x.FacebookUserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        builder.Entity<FacebookPost>(b =>
        {
            b.ToTable(DbTablePrefix + "FacebookPosts", DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.FacebookPostId).IsRequired().HasMaxLength(256);
            b.Property(x => x.Message).HasMaxLength(2048);
            b.Property(x => x.PostType).HasMaxLength(512);
            b.Property(x => x.AttachmentUrl).HasMaxLength(1024);
            b.Property(x => x.LinkUrl).HasMaxLength(1024);
            b.Property(x => x.PermalinkUrl).HasMaxLength(1024);
            b.Property(x => x.PictureUrl).HasMaxLength(1024);
            b.Property(x => x.FullPictureUrl).HasMaxLength(1024);

            b.HasIndex(x => x.FacebookPostId).IsUnique();
            b.HasIndex(x => x.FacebookPageId);

            b.HasOne(x => x.FacebookPage)
                .WithMany()
                .HasForeignKey(x => x.FacebookPageId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        builder.Entity<AutoReplyCampaign>(b =>
        {
            b.ToTable(DbTablePrefix + "AutoReplyCampaigns", DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.CampaignName).IsRequired().HasMaxLength(256);
            b.Property(x => x.Description).HasMaxLength(1024);
            b.Property(x => x.PublicReplyMessage).IsRequired().HasMaxLength(2048);
            b.Property(x => x.PrivateReplyMessage).HasMaxLength(2048);

            b.HasIndex(x => x.FacebookPostId);

            b.HasOne(x => x.FacebookPost)
                .WithMany()
                .HasForeignKey(x => x.FacebookPostId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        builder.Entity<CampaignActivity>(b =>
        {
            b.ToTable(DbTablePrefix + "CampaignActivities", DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.FacebookCommentId).IsRequired().HasMaxLength(256);
            b.Property(x => x.CommenterFacebookId).IsRequired().HasMaxLength(256);
            b.Property(x => x.CommenterName).HasMaxLength(256);
            b.Property(x => x.OriginalComment).HasMaxLength(2048);
            b.Property(x => x.PublicReplyMessage).HasMaxLength(2048);
            b.Property(x => x.PrivateReplyMessage).HasMaxLength(2048);
            b.Property(x => x.PublicReplyId).HasMaxLength(256);
            b.Property(x => x.PrivateMessageId).HasMaxLength(256);
            b.Property(x => x.ErrorMessage).HasMaxLength(512);

            b.HasIndex(x => x.FacebookCommentId).IsUnique();
            b.HasIndex(x => x.CampaignId);
            b.HasIndex(x => x.CommenterFacebookId);

            b.HasOne(x => x.Campaign)
                .WithMany()
                .HasForeignKey(x => x.CampaignId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }
}

