using System;
using Volo.Abp.Application.Dtos;

namespace FbAutoReplyPlatformExpress.Services.Dtos;

public class CampaignActivityDto : FullAuditedEntityDto<Guid>
{
    public Guid CampaignId { get; set; }
    public string FacebookCommentId { get; set; } = string.Empty;
    public string CommenterFacebookId { get; set; } = string.Empty;
    public string CommenterName { get; set; } = string.Empty;
    public string OriginalComment { get; set; } = string.Empty;
    public string? PublicReplyMessage { get; set; }
    public string? PrivateReplyMessage { get; set; }
    public string? PublicReplyId { get; set; }
    public string? PrivateMessageId { get; set; }
    public bool PublicReplySent { get; set; }
    public bool PrivateReplySent { get; set; }
    public DateTime? PublicReplyAt { get; set; }
    public DateTime? PrivateReplyAt { get; set; }
    public string? ErrorMessage { get; set; }
    public bool HasError { get; set; }
    public int RetryCount { get; set; }
    public DateTime CommentCreatedAt { get; set; }
    
    // Additional properties for UI
    public string CampaignName { get; set; } = string.Empty;
    public string PostMessage { get; set; } = string.Empty;
    public string PageName { get; set; } = string.Empty;
}

public class CreateCampaignActivityDto
{
    public Guid CampaignId { get; set; }
    public string FacebookCommentId { get; set; } = string.Empty;
    public string CommenterFacebookId { get; set; } = string.Empty;
    public string CommenterName { get; set; } = string.Empty;
    public string OriginalComment { get; set; } = string.Empty;
    public DateTime CommentCreatedAt { get; set; }
}

public class GetActivitiesInput : PagedAndSortedResultRequestDto
{
    public Guid? CampaignId { get; set; }
    public string? SearchText { get; set; }
    public bool? HasError { get; set; }
    public bool? PublicReplySent { get; set; }
    public bool? PrivateReplySent { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class WebhookCommentDto
{
    public string CommentId { get; set; } = string.Empty;
    public string PostId { get; set; } = string.Empty;
    public string CommenterFacebookId { get; set; } = string.Empty;
    public string CommenterName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedTime { get; set; }
}
