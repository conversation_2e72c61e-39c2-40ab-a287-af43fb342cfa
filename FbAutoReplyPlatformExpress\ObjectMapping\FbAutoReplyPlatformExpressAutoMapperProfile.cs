﻿using AutoMapper;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Services.Dtos;

namespace FbAutoReplyPlatformExpress.ObjectMapping;

public class FbAutoReplyPlatformExpressAutoMapperProfile : Profile
{
    public FbAutoReplyPlatformExpressAutoMapperProfile()
    {
        /* Create your AutoMapper object mappings here */

        // Facebook User mappings
        CreateMap<FacebookUser, FacebookUserDto>();
        CreateMap<CreateFacebookUserDto, FacebookUser>();
        CreateMap<UpdateFacebookUserDto, FacebookUser>();

        // Facebook Page mappings
        CreateMap<FacebookPage, FacebookPageDto>();
        CreateMap<CreateFacebookPageDto, FacebookPage>();
        CreateMap<UpdateFacebookPageDto, FacebookPage>();

        // Facebook Post mappings
        CreateMap<FacebookPost, FacebookPostDto>()
            .ForMember(dest => dest.PageName, opt => opt.Ignore())
            .ForMember(dest => dest.HasActiveCampaign, opt => opt.Ignore());
        CreateMap<CreateFacebookPostDto, FacebookPost>();
        CreateMap<UpdateFacebookPostDto, FacebookPost>();

        // Auto Reply Campaign mappings
        CreateMap<AutoReplyCampaign, AutoReplyCampaignDto>()
            .ForMember(dest => dest.PostMessage, opt => opt.Ignore())
            .ForMember(dest => dest.PageName, opt => opt.Ignore());
        CreateMap<CreateAutoReplyCampaignDto, AutoReplyCampaign>();
        CreateMap<UpdateAutoReplyCampaignDto, AutoReplyCampaign>();

        // Campaign Activity mappings
        CreateMap<CampaignActivity, CampaignActivityDto>()
            .ForMember(dest => dest.CampaignName, opt => opt.Ignore())
            .ForMember(dest => dest.PostMessage, opt => opt.Ignore())
            .ForMember(dest => dest.PageName, opt => opt.Ignore());
        CreateMap<CreateCampaignActivityDto, CampaignActivity>();
    }
}
