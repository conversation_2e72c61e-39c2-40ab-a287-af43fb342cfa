@page "/facebook/pages"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.Application.Dtos
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookPageService FacebookPageService
@inject IFacebookAuthService FacebookAuthService
@inject NavigationManager NavigationManager
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.ViewPages)]

<PageTitle>Facebook Pages</PageTitle>

<Card>
    <CardHeader>
        <Row Class="justify-content-between">
            <Column ColumnSize="ColumnSize.IsAuto">
                <h2>
                    <Icon Name="IconName.FileAlt" />
                    Facebook Pages
                </h2>
            </Column>
            <Column ColumnSize="ColumnSize.IsAuto">
                @if (HasManagePermission)
                {
                    <Button Color="Color.Primary" Clicked="ImportPagesAsync">
                        <Icon Name="IconName.Download" />
                        Import Pages
                    </Button>
                    <Button Color="Color.Secondary" Clicked="SyncAllPagesAsync" Class="ms-2">
                        <Icon Name="IconName.Sync" />
                        Sync All
                    </Button>
                }
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        @if (!IsConnected)
        {
            <Alert Color="Color.Warning" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                You need to connect your Facebook account first.
                <Button Color="Color.Link" Clicked="NavigateToConnection" Class="ms-2">
                    Connect Now
                </Button>
            </Alert>
        }
        else if (FacebookPages == null)  // Initial state before first load
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading Facebook Pages...</p>
            </div>
        }
        else if (IsLoading)
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Refreshing  Facebook Pages...</p>
            </div>
        }
        else if (FacebookPages.Items.Any())
        {
            <DataGrid TItem="FacebookPageDto"
                      Data="FacebookPages.Items"
                      ReadData="OnDataGridReadAsync"
                      TotalItems="(int)FacebookPages.TotalCount"
                      ShowPager="true"
                      PageSize="PageSize"
                      Responsive="true">
                <DataGridColumns>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.PageProfilePictureUrl)" Caption="" Sortable="false" Width="80px">
                        <DisplayTemplate>
                            @if (!string.IsNullOrEmpty(context.PageProfilePictureUrl))
                            {
                                <img src="@context.PageProfilePictureUrl" alt="Page Picture" style="width: 40px; height: 40px; border-radius: 50%;" />
                            }
                            else
                            {
                                <Icon Name="IconName.FileAlt" Style="font-size: 24px;" />
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.PageName)" Caption="Page Name" Sortable="true">
                        <DisplayTemplate>
                            <strong>@context.PageName</strong>
                            <br />
                            <small class="text-muted">@context.Category</small>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.FollowersCount)" Caption="Followers" Sortable="true">
                        <DisplayTemplate>
                            @context.FollowersCount.ToString("N0")
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.IsConnected)" Caption="Status" Sortable="true">
                        <DisplayTemplate>
                            @if (context.IsConnected)
                            {
                                <Badge Color="Color.Success">Connected</Badge>
                            }
                            else
                            {
                                <Badge Color="Color.Secondary">Disconnected</Badge>
                            }
                            @if (context.WebhookSubscribed)
                            {
                                <Badge Color="Color.Info" Class="ms-1">Webhook Active</Badge>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.LastSyncAt)" Caption="Last Sync" Sortable="true">
                        <DisplayTemplate>
                            @if (context.LastSyncAt.HasValue)
                            {
                                @context.LastSyncAt.Value.ToString("MMM dd, yyyy HH:mm")
                            }
                            else
                            {
                                <span class="text-muted">Never</span>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Caption="Actions" Sortable="false" Width="200px">
                        <DisplayTemplate>
                            @if (HasManagePermission)
                            {
                                <Dropdown>
                                    <DropdownToggle Color="Color.Primary" Size="Size.Small">
                                        Actions
                                    </DropdownToggle>
                                    <DropdownMenu>
                                        <DropdownItem Clicked="() => SyncPageAsync(context.Id)">
                                            <Icon Name="IconName.Sync" />
                                            Sync
                                        </DropdownItem>
                                        @if (context.IsConnected && !context.WebhookSubscribed)
                                        {
                                            <DropdownItem Clicked="() => SubscribeWebhookAsync(context.Id)">
                                                <Icon Name="IconName.Bell" />
                                                Enable Webhook
                                            </DropdownItem>
                                        }
                                        @if (context.WebhookSubscribed)
                                        {
                                            <DropdownItem Clicked="() => UnsubscribeWebhookAsync(context.Id)">
                                                <Icon Name="IconName.BellSlash" />
                                                Disable Webhook
                                            </DropdownItem>
                                        }
                                        <DropdownDivider />
                                        @if (context.IsConnected)
                                        {
                                            <DropdownItem Clicked="() => DisconnectPageAsync(context.Id)">
                                                <Icon Name="IconName.Times" />
                                                Disconnect
                                            </DropdownItem>
                                        }
                                        else
                                        {
                                            <DropdownItem Clicked="() => ReconnectPageAsync(context.Id)">
                                                <Icon Name="IconName.Link" />
                                                Reconnect
                                            </DropdownItem>
                                        }
                                        <DropdownDivider />
                                        <DropdownItem Clicked="() => ViewPostsAsync(context.Id)" Class="text-primary">
                                            <Icon Name="IconName.Comments" />
                                            View Posts
                                        </DropdownItem>
                                    </DropdownMenu>
                                </Dropdown>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                </DataGridColumns>
            </DataGrid>
        }
        else
        {
            <Alert Color="Color.Info" Visible="true">
                <Icon Name="IconName.Info" />
                No Facebook Pages found. Click "Import Pages" to import your Facebook Pages.
            </Alert>
        }
    </CardBody>
</Card>

<!-- Import Pages Modal -->
<Modal @ref="ImportPagesModal">
    <ModalContent Centered="true" Size="ModalSize.Large">
        <ModalHeader>
            <ModalTitle>Import Facebook Pages</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            @if (AvailablePages?.Any() == true)
            {
                <p>Select the pages you want to import:</p>
                @foreach (var pageItem in AvailablePages)
                {
                    <Card Class="mb-2">
                        <CardBody>
                            <Row>
                                <Column ColumnSize="ColumnSize.IsAuto">
                                    <Check TValue="bool" @bind-Checked="@pageItem.IsSelected" />
                                </Column>
                                <Column ColumnSize="ColumnSize.IsAuto">
                                    @if (!string.IsNullOrEmpty(pageItem.PageProfilePictureUrl))
                                    {
                                        <img src="@pageItem.PageProfilePictureUrl" alt="Page Picture" style="width: 40px; height: 40px; border-radius: 50%;" />
                                    }
                                </Column>
                                <Column>
                                    <strong>@pageItem.PageName</strong>
                                    <br />
                                    <small class="text-muted">@pageItem.Category • @pageItem.FollowersCount.ToString("N0") followers</small>
                                </Column>
                            </Row>
                        </CardBody>
                    </Card>
                }
            }
            else if (IsLoadingAvailablePages)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading available pages...</p>
                </div>
            }
            else
            {
                <Alert Color="Color.Warning" Visible="true">
                    No pages available for import.
                </Alert>
            }
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="CloseImportModal">Cancel</Button>
            <Button Color="Color.Primary" Clicked="ImportSelectedPagesAsync" Disabled="@(!AvailablePages?.Any(p => p.IsSelected) == true)">
                Import Selected Pages
            </Button>
        </ModalFooter>
    </ModalContent>
</Modal>

@code {
    private PagedResultDto<FacebookPageDto>? FacebookPages;
    private bool IsLoading = false;
    private bool IsConnected = false;
    private bool HasManagePermission = false;
    private int PageSize = 10;
    private int CurrentPage = 1;
    private string CurrentSorting = "";

    // Import Pages Modal
    private Modal ImportPagesModal = default!;
    private List<SelectableFacebookPageDto>? AvailablePages;
    private bool IsLoadingAvailablePages = false;

    protected override async Task OnInitializedAsync()
    {
        HasManagePermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Facebook.ManagePages);
        await CheckConnectionAsync();

        // Load initial data if connected
        if (IsConnected)
        {
            await LoadPages();
        }
        else
        {
            // Initialize with empty result to show the "not connected" state properly
            FacebookPages = new PagedResultDto<FacebookPageDto>(0, new List<FacebookPageDto>());
        }
    }

    private async Task CheckConnectionAsync()
    {
        try
        {
            var wasConnected = IsConnected;
            IsConnected = await FacebookAuthService.IsConnectedToFacebookAsync();

            // If connection status changed from false to true, load the pages
            if (!wasConnected && IsConnected)
            {
                await LoadPages();
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error checking Facebook connection: {ex.Message}");
        }
    }

    private async Task LoadPages()
    {
        if (!IsConnected)
        {
            FacebookPages = null;
            return;
        }

        try
        {
            IsLoading = true;
            var request = new PagedAndSortedResultRequestDto
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };

            FacebookPages = await FacebookPageService.GetListAsync(request);
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading pages: {ex.Message}");

            FacebookPages = new PagedResultDto<FacebookPageDto>(0, new List<FacebookPageDto>());
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<FacebookPageDto> e)
    {
        if (!IsConnected) return;

        // Prevent infinite loop - only reload if pagination/sorting actually changed
        var newPage = e.Page;
        var newPageSize = e.PageSize;
        var newSorting = string.Join(",", e.Columns
            .Where(c => c.SortDirection != SortDirection.Default)
            .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : "")));

        // Only reload if something actually changed
        if (newPage != CurrentPage || newPageSize != PageSize || newSorting != CurrentSorting)
        {
            CurrentPage = newPage;
            PageSize = newPageSize;
            CurrentSorting = newSorting;

            await LoadPages();
        }
    }

    private void NavigateToConnection()
    {
        NavigationManager.NavigateTo("/facebook/connection");
    }

    private async Task ImportPagesAsync()
    {
        try
        {
            IsLoadingAvailablePages = true;
            await ImportPagesModal.Show();

            var availablePages = await FacebookPageService.GetAvailablePagesFromFacebookAsync();
            AvailablePages = availablePages.Select(p => new SelectableFacebookPageDto(p)).ToList();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading available pages: {ex.Message}");
        }
        finally
        {
            IsLoadingAvailablePages = false;
        }
    }

    private async Task CloseImportModal()
    {
        await ImportPagesModal.Hide();
        AvailablePages = null;
    }

    private async Task ImportSelectedPagesAsync()
    {
        try
        {
            var selectedPages = AvailablePages?.Where(p => p.IsSelected).Select(p => p.ToImportDto()).ToList();
            if (selectedPages?.Any() == true)
            {
                await FacebookPageService.ImportMultiplePagesAsync(selectedPages);
                await Message.Success($"Successfully imported {selectedPages.Count} page(s)!");
                await CloseImportModal();
                await LoadPages();
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error importing pages: {ex.Message}");
        }
    }

    private async Task SyncPageAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.SyncPageInfoAsync(pageId);
            await Message.Success("Page synced successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error syncing page: {ex.Message}");
        }
    }

    private async Task SyncAllPagesAsync()
    {
        try
        {
            await FacebookPageService.SyncAllPagesAsync();
            await Message.Success("All pages synced successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error syncing pages: {ex.Message}");
        }
    }

    private async Task SubscribeWebhookAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.SubscribeToWebhookAsync(pageId);
            await Message.Success("Webhook enabled successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error enabling webhook: {ex.Message}");
        }
    }

    private async Task UnsubscribeWebhookAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.UnsubscribeFromWebhookAsync(pageId);
            await Message.Success("Webhook disabled successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error disabling webhook: {ex.Message}");
        }
    }

    private async Task DisconnectPageAsync(Guid pageId)
    {
        var confirmed = await Message.Confirm("Are you sure you want to disconnect this page?");
        if (confirmed)
        {
            try
            {
                await FacebookPageService.DisconnectPageAsync(pageId);
                await Message.Success("Page disconnected successfully!");
                await LoadPages();
            }
            catch (Exception ex)
            {
                await Message.Error($"Error disconnecting page: {ex.Message}");
            }
        }
    }

    private async Task ReconnectPageAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.ReconnectPageAsync(pageId);
            await Message.Success("Page reconnected successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error reconnecting page: {ex.Message}");
        }
    }

    private void ViewPostsAsync(Guid pageId)
    {
        NavigationManager.NavigateTo($"/facebook/posts?pageId={pageId}");
    }

    public class SelectableFacebookPageDto
    {
        public FacebookPageImportDto ImportDto { get; set; }
        public bool IsSelected { get; set; }

        public SelectableFacebookPageDto(FacebookPageImportDto importDto)
        {
            ImportDto = importDto;
            IsSelected = false;
        }

        public string FacebookPageId => ImportDto.FacebookPageId;
        public string PageName => ImportDto.PageName;
        public string? PageProfilePictureUrl => ImportDto.PageProfilePictureUrl;
        public string? Category => ImportDto.Category;
        public int FollowersCount => ImportDto.FollowersCount;

        public FacebookPageImportDto ToImportDto() => ImportDto;
    }
}
