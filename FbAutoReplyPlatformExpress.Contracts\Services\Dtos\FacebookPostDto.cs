using System;
using Volo.Abp.Application.Dtos;

namespace FbAutoReplyPlatformExpress.Services.Dtos;

public class FacebookPostDto : FullAuditedEntityDto<Guid>
{
    public string FacebookPostId { get; set; } = string.Empty;
    public Guid FacebookPageId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PostType { get; set; } = string.Empty;
    public string? AttachmentUrl { get; set; }
    public string? LinkUrl { get; set; }
    public string? PermalinkUrl { get; set; }
    public string? PictureUrl { get; set; }
    public string? FullPictureUrl { get; set; }
    public DateTime FacebookCreatedTime { get; set; }
    public int LikesCount { get; set; }
    public int CommentsCount { get; set; }
    public int SharesCount { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastSyncAt { get; set; }

    // Additional properties for UI
    public string PageName { get; set; } = string.Empty;
    public bool HasActiveCampaign { get; set; }
}

public class CreateFacebookPostDto
{
    public string FacebookPostId { get; set; } = string.Empty;
    public Guid FacebookPageId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PostType { get; set; } = string.Empty;
    public string? AttachmentUrl { get; set; }
    public string? LinkUrl { get; set; }
    public string? PermalinkUrl { get; set; }
    public string? PictureUrl { get; set; }
    public string? FullPictureUrl { get; set; }
    public DateTime FacebookCreatedTime { get; set; }
    public int LikesCount { get; set; }
    public int CommentsCount { get; set; }
    public int SharesCount { get; set; }
}

public class UpdateFacebookPostDto
{
    public string? Message { get; set; }
    public string? AttachmentUrl { get; set; }
    public string? LinkUrl { get; set; }
    public int? LikesCount { get; set; }
    public int? CommentsCount { get; set; }
    public int? SharesCount { get; set; }
}

public class GetPostsInput : PagedAndSortedResultRequestDto
{
    public Guid? FacebookPageId { get; set; }
    public string? SearchText { get; set; }
    public bool? HasActiveCampaign { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
